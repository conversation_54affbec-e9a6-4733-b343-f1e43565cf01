import pandas as pd
import numpy as np

def analyze_merged_data():
    """
    Analyze the merged transaction data
    """
    try:
        # Load the merged data
        merged_df = pd.read_excel("merged_transactions.xlsx")
        
        print("=== MERGED TRANSACTIONS ANALYSIS ===")
        print(f"Total merged transactions: {len(merged_df):,}")
        print(f"Total columns: {len(merged_df.columns)}")
        print()
        
        # Basic statistics
        print("=== AMOUNT COMPARISON ===")
        print("Amount from TransactionDetail vs AccountStatement:")
        print(f"TransactionDetail amount - Mean: {merged_df['amount'].mean():,.2f}")
        print(f"AccountStatement amount - Mean: {merged_df['TranAmt'].mean():,.2f}")
        print()
        
        # Check for amount differences
        merged_df['amount_difference'] = merged_df['amount'] - merged_df['TranAmt']
        print(f"Amount differences (TransactionDetail - AccountStatement):")
        print(f"Mean difference: {merged_df['amount_difference'].mean():.2f}")
        print(f"Max difference: {merged_df['amount_difference'].max():.2f}")
        print(f"Min difference: {merged_df['amount_difference'].min():.2f}")
        print(f"Records with exact match: {(merged_df['amount_difference'] == 0).sum():,}")
        print()
        
        # Transaction types
        print("=== TRANSACTION TYPES ===")
        print(merged_df['transaction_type'].value_counts())
        print()
        
        # Transaction status
        print("=== TRANSACTION STATUS ===")
        print(merged_df['transaction_status'].value_counts())
        print()
        
        # Date range
        print("=== DATE RANGE ===")
        merged_df['TranDate'] = pd.to_datetime(merged_df['TranDate'])
        print(f"Date range: {merged_df['TranDate'].min()} to {merged_df['TranDate'].max()}")
        print()
        
        # Top banks
        print("=== TOP 10 BANKS ===")
        print(merged_df['bank_name'].value_counts().head(10))
        print()
        
        # Sample of merged data
        print("=== SAMPLE MERGED RECORDS ===")
        sample_cols = ['session_id', 'beneficiary_account_name', 'amount', 'TranAmt', 'transaction_status', 'bank_name']
        print(merged_df[sample_cols].head(10))
        
        return merged_df
        
    except Exception as e:
        print(f"Error analyzing merged data: {str(e)}")
        return None

if __name__ == "__main__":
    analyze_merged_data()
