import pandas as pd
import os

def load_excel_files():
    """
    Load the two Excel spreadsheets in the current directory
    """
    # Check if files exist
    account_statement_file = "AccountStatement.xlsx"
    transaction_detail_file = "TransactionDetail.xlsx"
    
    if not os.path.exists(account_statement_file):
        print(f"Error: {account_statement_file} not found")
        return None, None
    
    if not os.path.exists(transaction_detail_file):
        print(f"Error: {transaction_detail_file} not found")
        return None, None
    
    try:
        # Load AccountStatement.xlsx
        print(f"Loading {account_statement_file}...")
        account_statement_df = pd.read_excel(account_statement_file)
        print(f"✓ Loaded {account_statement_file} - Shape: {account_statement_df.shape}")
        print(f"Columns: {list(account_statement_df.columns)}")
        print()
        
        # Load TransactionDetail.xlsx
        print(f"Loading {transaction_detail_file}...")
        transaction_detail_df = pd.read_excel(transaction_detail_file)
        print(f"✓ Loaded {transaction_detail_file} - Shape: {transaction_detail_df.shape}")
        print(f"Columns: {list(transaction_detail_df.columns)}")
        print()
        
        return account_statement_df, transaction_detail_df
        
    except Exception as e:
        print(f"Error loading Excel files: {str(e)}")
        return None, None

def display_data_preview(df, filename, num_rows=5):
    """
    Display a preview of the dataframe
    """
    print(f"\n--- Preview of {filename} (first {num_rows} rows) ---")
    print(df.head(num_rows))
    print(f"\nData types:")
    print(df.dtypes)
    print(f"\nBasic info:")
    print(f"Total rows: {len(df)}")
    print(f"Total columns: {len(df.columns)}")
    print("-" * 50)

if __name__ == "__main__":
    # Load the Excel files
    account_df, transaction_df = load_excel_files()
    
    if account_df is not None and transaction_df is not None:
        print("Both Excel files loaded successfully!")
        
        # Display previews
        display_data_preview(account_df, "AccountStatement.xlsx")
        display_data_preview(transaction_df, "TransactionDetail.xlsx")
        
        # You can now work with the dataframes
        # account_df contains the AccountStatement data
        # transaction_df contains the TransactionDetail data
        
    else:
        print("Failed to load one or both Excel files.")
