import pandas as pd
import os

def load_and_examine_session_ids():
    """
    Load both Excel files and examine the session ID columns
    """
    try:
        # Load both files
        print("Loading Excel files...")
        account_df = pd.read_excel("AccountStatement.xlsx")
        transaction_df = pd.read_excel("TransactionDetail.xlsx")
        
        print(f"AccountStatement shape: {account_df.shape}")
        print(f"TransactionDetail shape: {transaction_df.shape}")
        print()
        
        # Check if session_id exists in AccountStatement
        print("Columns in AccountStatement:")
        print(list(account_df.columns))
        print()
        
        print("Columns in TransactionDetail:")
        print(list(transaction_df.columns))
        print()
        
        # Look for session ID related columns in AccountStatement
        session_cols_account = [col for col in account_df.columns if 'session' in col.lower() or 'id' in col.lower()]
        print(f"Potential session/ID columns in AccountStatement: {session_cols_account}")
        
        # Check session_id in TransactionDetail
        if 'session_id' in transaction_df.columns:
            print(f"session_id column found in TransactionDetail")
            print(f"Sample session_id values from TransactionDetail:")
            print(transaction_df['session_id'].head(10))
            print(f"Non-null session_id count: {transaction_df['session_id'].notna().sum()}")
            print(f"Unique session_id count: {transaction_df['session_id'].nunique()}")
        
        return account_df, transaction_df
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return None, None

def find_matching_column(account_df, transaction_df):
    """
    Try to find the matching column between the two datasets
    """
    print("\n=== Analyzing potential matching columns ===")
    
    # Check if any column in AccountStatement might contain session IDs
    # Let's look at TranId and TranRmks as potential matches
    
    if 'session_id' in transaction_df.columns:
        session_ids = transaction_df['session_id'].dropna().unique()
        print(f"Sample session IDs from TransactionDetail: {session_ids[:5]}")
        
        # Check if any of these session IDs appear in AccountStatement columns
        for col in account_df.columns:
            if account_df[col].dtype == 'object':  # Only check string columns
                matches = account_df[col].astype(str).isin(session_ids.astype(str)).sum()
                if matches > 0:
                    print(f"Found {matches} matches between session_id and {col}")
                    return col
    
    return None

def merge_datasets(account_df, transaction_df, matching_col):
    """
    Merge the datasets based on the matching column
    """
    if matching_col is None:
        print("No matching column found. Let's try a different approach...")
        return None
    
    print(f"\n=== Merging datasets using {matching_col} ===")
    
    # Prepare the merge
    # Rename the matching column in AccountStatement to session_id for consistency
    account_merge = account_df.copy()
    if matching_col != 'session_id':
        account_merge = account_merge.rename(columns={matching_col: 'session_id'})
    
    # Select relevant columns from AccountStatement
    account_cols = ['session_id', 'TranDate', 'ValueDate', 'DrAmt', 'CrAmt', 'TranAmt', 'TranParticular']
    account_merge = account_merge[account_cols]
    
    # Merge the datasets
    merged_df = pd.merge(
        transaction_df, 
        account_merge, 
        on='session_id', 
        how='inner',  # Only keep records that exist in both datasets
        suffixes=('_detail', '_statement')
    )
    
    print(f"Merged dataset shape: {merged_df.shape}")
    print(f"Columns in merged dataset: {len(merged_df.columns)}")
    
    return merged_df

def create_final_output(merged_df):
    """
    Create the final output with selected columns
    """
    if merged_df is None or merged_df.empty:
        print("No merged data to process")
        return None
    
    # Select key columns for the final output
    output_columns = [
        'session_id',
        'id',  # Transaction ID from TransactionDetail
        'created_at',
        'beneficiary_account_name',
        'beneficiary_account_number',
        'amount',  # Amount from TransactionDetail
        'TranAmt',  # Amount from AccountStatement
        'DrAmt',   # Debit amount from AccountStatement
        'CrAmt',   # Credit amount from AccountStatement
        'TranDate',  # Transaction date from AccountStatement
        'transaction_type',
        'transaction_status',
        'bank_name',
        'narration',
        'TranParticular'  # Transaction particulars from AccountStatement
    ]
    
    # Only include columns that exist in the merged dataset
    available_columns = [col for col in output_columns if col in merged_df.columns]
    final_df = merged_df[available_columns].copy()
    
    print(f"\nFinal output columns: {available_columns}")
    print(f"Final dataset shape: {final_df.shape}")
    
    return final_df

if __name__ == "__main__":
    # Load and examine the data
    account_df, transaction_df = load_and_examine_session_ids()
    
    if account_df is not None and transaction_df is not None:
        # Find matching column
        matching_col = find_matching_column(account_df, transaction_df)
        
        if matching_col:
            # Merge the datasets
            merged_df = merge_datasets(account_df, transaction_df, matching_col)
            
            if merged_df is not None and not merged_df.empty:
                # Create final output
                final_df = create_final_output(merged_df)
                
                if final_df is not None:
                    # Save to new Excel file
                    output_filename = "merged_transactions.xlsx"
                    final_df.to_excel(output_filename, index=False)
                    print(f"\n✓ Merged data saved to {output_filename}")
                    
                    # Display preview
                    print(f"\nPreview of merged data (first 5 rows):")
                    print(final_df.head())
                    
                else:
                    print("Failed to create final output")
            else:
                print("No data was merged successfully")
        else:
            print("Could not find a matching column between the datasets")
            print("Please check if the session IDs are stored in a different column in AccountStatement")
    else:
        print("Failed to load the Excel files")
